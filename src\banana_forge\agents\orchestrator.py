"""
Multi-agent orchestrator for Banana Forge.

This module contains the main orchestration logic that coordinates
the supervisor and specialized agents to generate comprehensive
feature implementation plans.
"""

import logging
from datetime import datetime
from typing import Any

from langchain_core.language_models import BaseLanguageModel
from langgraph.graph import START, MessagesState, StateGraph

from ..config import settings
from ..llm import LLMClient
from ..validation import fix_plan_issues, validate_implementation_plan
from .agent_definitions import create_specialized_agents
from .compression import AgentOutputCompressor
from .supervisor import create_supervisor_agent

logger = logging.getLogger(__name__)


class MultiAgentOrchestrator:
    """
    Orchestrates the multi-agent system for feature implementation planning.

    This class manages the creation and coordination of the supervisor agent
    and specialized agents, handling the complete workflow from feature
    description to comprehensive implementation plan.
    """

    def __init__(self):
        """Initialize the multi-agent orchestrator."""
        self.llm_client = LLMClient()
        self.compressor = AgentOutputCompressor()
        self.agents: dict[str, Any] = {}
        self.supervisor = None
        self.graph = None
        self._setup_agents()
        self._setup_graph()

    def _setup_agents(self) -> None:
        """Set up the specialized agents and supervisor."""
        try:
            # Create local model for specialized agents
            local_model = self._create_local_model()

            # Create specialized agents
            self.agents = create_specialized_agents(local_model)
            agent_names = list(self.agents.keys())

            # Create primary model for supervisor
            primary_model = self._create_primary_model()

            # Create supervisor agent
            self.supervisor = create_supervisor_agent(primary_model, agent_names)

            logger.info(f"Successfully set up {len(self.agents)} agents and supervisor")

        except Exception as e:
            logger.error(f"Failed to setup agents: {e}")
            raise

    def _create_local_model(self) -> BaseLanguageModel:
        """Create a local model instance for specialized agents."""

        # For now, we'll use a simple wrapper around our LLM client
        # In a full implementation, this would be a proper LangChain model
        class LocalModelWrapper:
            def __init__(self, llm_client: LLMClient):
                self.llm_client = llm_client

            def invoke(self, messages, **kwargs):
                # Convert messages to a simple prompt for our LLM client
                if isinstance(messages, list):
                    prompt = "\n".join(
                        [msg.get("content", str(msg)) for msg in messages]
                    )
                else:
                    prompt = str(messages)

                response = self.llm_client.generate_completion(
                    prompt=prompt,
                    model=settings.local_model,
                    max_tokens=2000,
                    temperature=0.7,
                )

                return {"content": response}

        return LocalModelWrapper(self.llm_client)

    def _create_primary_model(self) -> BaseLanguageModel:
        """Create a primary model instance for the supervisor."""

        class PrimaryModelWrapper:
            def __init__(self, llm_client: LLMClient):
                self.llm_client = llm_client

            def invoke(self, messages, **kwargs):
                # Convert messages to a simple prompt for our LLM client
                if isinstance(messages, list):
                    prompt = "\n".join(
                        [msg.get("content", str(msg)) for msg in messages]
                    )
                else:
                    prompt = str(messages)

                response = self.llm_client.generate_completion(
                    prompt=prompt,
                    model=settings.primary_model,
                    max_tokens=4000,
                    temperature=0.7,
                )

                return {"content": response}

        return PrimaryModelWrapper(self.llm_client)

    def _setup_graph(self) -> None:
        """Set up the LangGraph workflow."""
        try:
            # Create the state graph
            graph = StateGraph(MessagesState)

            # Add supervisor node
            graph.add_node("supervisor", self.supervisor)

            # Add specialized agent nodes
            for agent_name, agent in self.agents.items():
                graph.add_node(agent_name, agent)
                # Add edges back to supervisor
                graph.add_edge(agent_name, "supervisor")

            # Set entry point
            graph.add_edge(START, "supervisor")

            # Compile the graph
            self.graph = graph.compile()

            logger.info("Successfully set up multi-agent graph")

        except Exception as e:
            logger.error(f"Failed to setup graph: {e}")
            raise

    def generate_plan(
        self,
        feature_description: str,
        additional_context: str = "",
        max_iterations: int = None,
    ) -> str:
        """
        Generate a comprehensive feature implementation plan using the
        multi-agent system.

        Args:
            feature_description: Description of the feature to implement
            additional_context: Additional context or requirements
            max_iterations: Maximum number of supervisor iterations

        Returns:
            Comprehensive implementation plan in markdown format
        """
        if max_iterations is None:
            max_iterations = settings.max_iterations

        try:
            logger.info(f"Starting multi-agent planning for: {feature_description}")

            # Prepare initial message
            initial_message = {
                "role": "user",
                "content": f"""Feature Request: {feature_description}

Additional Context: {additional_context if additional_context else "None provided"}

Please orchestrate your team of specialized agents to analyze this feature
request comprehensively.
Delegate specific tasks to relevant agents to gather information about:
- Core implementation requirements
- User workflows and interactions
- Error handling and edge cases
- Data management needs
- UI/UX considerations
- Integration requirements
- Monitoring and observability
- Type definitions and contracts

After gathering agent insights, synthesize a comprehensive implementation plan.""",
            }

            # Execute the multi-agent workflow with iteration control
            result = self.graph.invoke(
                {"messages": [initial_message]},
                config={
                    "recursion_limit": max_iterations
                    * 10,  # Allow for multiple agent calls per iteration
                    "max_iterations": max_iterations,
                },
            )

            # Extract agent outputs from the workflow
            agent_outputs = self._extract_agent_outputs(result)

            # Compress agent outputs for final synthesis
            compressed_outputs = self.compressor.compress_multiple_outputs(
                agent_outputs,
                max_length_per_agent=settings.max_code_snippets
                * 100,  # Adjust based on config
            )

            # Generate final synthesis using the primary model
            synthesis_prompt = self.compressor.create_synthesis_prompt(
                feature_description=feature_description,
                additional_context=additional_context,
                compressed_outputs=compressed_outputs,
            )

            # Use primary model for final synthesis
            final_plan = self.llm_client.generate_completion(
                prompt=synthesis_prompt,
                model=settings.primary_model,
                max_tokens=8000,
                temperature=0.7,
            )

            # Validate and fix the plan
            validation_result = validate_implementation_plan(
                final_plan, feature_description
            )

            if settings.verbose:
                logger.info(f"Plan validation score: {validation_result['score']:.2f}")
                if validation_result["suggestions"]:
                    logger.info(
                        f"Validation suggestions: {validation_result['suggestions']}"
                    )

            # Apply automatic fixes
            fixed_plan = fix_plan_issues(final_plan, feature_description)

            # Post-process the plan
            formatted_plan = self._format_final_plan(
                fixed_plan, feature_description, additional_context, validation_result
            )

            logger.info("Multi-agent planning completed successfully")
            return formatted_plan

        except Exception as e:
            logger.error(f"Multi-agent planning failed: {e}")
            raise

    def _extract_agent_outputs(
        self, workflow_result: dict[str, Any]
    ) -> dict[str, dict[str, Any]]:
        """
        Extract individual agent outputs from the workflow result.

        Args:
            workflow_result: Result from the LangGraph workflow execution

        Returns:
            Dictionary mapping agent names to their output data
        """
        agent_outputs = {}

        try:
            # Extract messages from the workflow result
            messages = workflow_result.get("messages", [])

            # Parse messages to identify agent outputs
            current_agent = None
            current_task = None

            for message in messages:
                content = ""
                if hasattr(message, "content"):
                    content = message.content
                elif isinstance(message, dict):
                    content = message.get("content", "")
                else:
                    content = str(message)

                # Check if this is a transfer message
                if "Successfully transferred to" in content:
                    # Extract agent name and task
                    parts = content.split("with task:")
                    if len(parts) > 1:
                        current_task = parts[1].strip()
                        # Extract agent name
                        agent_part = (
                            parts[0].replace("Successfully transferred to", "").strip()
                        )
                        current_agent = agent_part

                # Check if this is an agent response
                elif (
                    current_agent
                    and content
                    and not content.startswith("Successfully transferred")
                ):
                    # This is likely an agent's response
                    if current_agent not in agent_outputs:
                        agent_outputs[current_agent] = {
                            "task": current_task or f"Analysis by {current_agent}",
                            "output": content,
                        }
                    else:
                        # Append to existing output
                        agent_outputs[current_agent]["output"] += "\n\n" + content

            # If no agent outputs were extracted, create mock outputs for testing
            if not agent_outputs:
                logger.warning(
                    "No agent outputs extracted from workflow, creating mock outputs"
                )
                for agent_name in self.agents.keys():
                    agent_outputs[agent_name] = {
                        "task": f"Analysis by {agent_name}",
                        "output": f"Mock output from {agent_name} - workflow "
                        f"extraction failed",
                    }

            logger.info(f"Extracted outputs from {len(agent_outputs)} agents")
            return agent_outputs

        except Exception as e:
            logger.error(f"Failed to extract agent outputs: {e}")
            # Return empty dict as fallback
            return {}

    def _format_final_plan(
        self,
        plan_content: str,
        feature_description: str,
        additional_context: str,
        validation_result: dict[str, Any] | None = None,
    ) -> str:
        """Format the final implementation plan with proper structure."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Ensure proper markdown structure
        if not plan_content.strip().startswith("#"):
            plan_content = (
                f"# {feature_description} Implementation Plan\n\n{plan_content}"
            )

        # Add metadata footer with validation info
        footer_parts = [
            "",
            "---",
            f"*Generated by Banana Forge Multi-Agent System on {timestamp}*",
            "",
            f"**Agents Involved:** {len(self.agents)} specialized agents + supervisor",
            f"**Feature:** {feature_description}",
            f"**Additional Context:** "
            f"{additional_context if additional_context else 'None'}",
        ]

        # Add validation information if available
        if validation_result:
            footer_parts.extend(
                [
                    f"**Quality Score:** {validation_result['score']:.2f}/1.0",
                    f"**Sections Found:** {len(validation_result['sections_found'])}",
                    f"**Word Count:** {validation_result['word_count']}",
                ]
            )

        footer = "\n".join(footer_parts)

        return plan_content + footer

    def get_agent_status(self) -> dict[str, Any]:
        """Get status information about the multi-agent system."""
        return {
            "total_agents": len(self.agents),
            "agent_names": list(self.agents.keys()),
            "supervisor_configured": self.supervisor is not None,
            "graph_compiled": self.graph is not None,
            "local_model": settings.local_model,
            "primary_model": settings.primary_model,
            "max_concurrent_agents": settings.max_concurrent_agents,
            "max_iterations": settings.max_iterations,
        }
