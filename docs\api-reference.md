# Banana Forge API Reference

This document provides detailed information about Banana Forge's CLI commands and Python API.

## CLI Commands

### `banana-forge generate`

Generate a structured feature implementation plan using AI agents.

**Syntax:**
```bash
banana-forge generate <feature_description> [OPTIONS]
```

**Arguments:**
- `feature_description` (required): Description of the feature to generate a plan for

**Options:**
- `--input, -i PATH`: Input file with additional context or specifications
- `--output, -o PATH`: Output file to save the generated plan (default: stdout)
- `--verbose, -v`: Enable verbose logging to see detailed progress
- `--dry-run`: Show what would be done without calling AI models

**Examples:**
```bash
# Basic usage
banana-forge generate "OAuth login system"

# With input and output files
banana-forge generate "Payment integration" -i requirements.txt -o payment_plan.md

# Verbose mode with dry run
banana-forge generate "Real-time chat" --verbose --dry-run
```

### `banana-forge version`

Show the version of Banana Forge.

**Syntax:**
```bash
banana-forge version
```

### `banana-forge config`

Show current configuration settings.

**Syntax:**
```bash
banana-forge config
```

**Output includes:**
- OpenRouter API Key status
- Ollama Base URL
- Primary and Local model settings
- Max concurrent agents
- ChromaDB path
- Log level

### `banana-forge index`

Index project files into ChromaDB for semantic search.

**Syntax:**
```bash
banana-forge index [OPTIONS]
```

**Options:**
- `--path, -p PATH`: Path to index (defaults to current project)
- `--reset`: Reset the collection before indexing
- `--verbose, -v`: Enable verbose output

**Examples:**
```bash
# Index current project
banana-forge index

# Index specific directory with reset
banana-forge index --path ./src --reset --verbose
```

### `banana-forge search`

Search the indexed codebase for relevant snippets.

**Syntax:**
```bash
banana-forge search <query> [OPTIONS]
```

**Arguments:**
- `query` (required): Search query to find relevant code

**Options:**
- `--limit, -l INTEGER`: Maximum number of results (default: 5)
- `--verbose, -v`: Show detailed results including distances

**Examples:**
```bash
# Basic search
banana-forge search "authentication patterns"

# Detailed search with more results
banana-forge search "error handling" --limit 10 --verbose
```

### `banana-forge db-stats`

Show ChromaDB collection statistics.

**Syntax:**
```bash
banana-forge db-stats
```

**Output includes:**
- Collection name
- Total documents
- Database path
- Sample file types

## Python API

### Core Functions

#### `run_generation()`

Main function for generating feature implementation plans.

```python
from banana_forge.core import run_generation

def run_generation(
    feature_description: str,
    additional_context: str = "",
    dry_run: bool = False,
) -> str:
    """
    Generate a comprehensive feature implementation plan.

    Args:
        feature_description: The feature to generate a plan for
        additional_context: Optional additional context or specifications
        dry_run: If True, return a mock response without calling AI models

    Returns:
        str: The generated implementation plan in Markdown format

    Raises:
        ValueError: If required configuration is missing
        Exception: If generation fails
    """
```

**Example:**
```python
from banana_forge.core import run_generation

# Generate a plan
plan = run_generation(
    feature_description="User authentication system",
    additional_context="Using FastAPI and PostgreSQL",
    dry_run=False
)

print(plan)
```

### Configuration

#### `Settings` Class

Configuration management using Pydantic BaseSettings.

```python
from banana_forge.config import settings

# Access configuration
print(settings.primary_model)
print(settings.openrouter_api_key)

# Validate configuration
try:
    settings.validate_required_settings()
    print("Configuration is valid")
except ValueError as e:
    print(f"Configuration error: {e}")
```

**Key Settings:**
- `gemini_api_key`: Google Gemini API key (free tier available)
- `openrouter_api_key`: OpenRouter API key (fallback)
- `primary_model`: Primary model for generation (default: "moonshotai/kimi-k2")
- `local_model`: Local model for intermediate tasks (default: "qwen2.5:8b")
- `max_concurrent_agents`: Maximum concurrent agents (default: 5)
- `chroma_db_path`: ChromaDB storage path
- `verbose`: Enable verbose logging

### LLM Client

#### `LLMClient` Class

Unified interface for interacting with different LLM providers with intelligent fallback.

```python
from banana_forge.llm import LLMClient

# Initialize client
client = LLMClient()

# Generate completion with automatic fallback
response = client.generate_completion(
    prompt="Generate a plan for user authentication",
    model="gemini-2.5-flash",  # Will try Gemini first, then fallback
    max_tokens=4000,
    temperature=0.7
)

# Check Gemini usage stats
stats = client.get_gemini_usage_stats()
print(f"Gemini usage: {stats['today']['requests']}/{stats['daily_limit']}")

# Disable fallback for specific model
response = client.generate_completion(
    prompt="Generate a plan for user authentication",
    model="moonshotai/kimi-k2",
    max_tokens=4000,
    temperature=0.7,
    use_fallback=False  # Direct routing only
)

# Check health
health = client.health_check()
print(health)  # {"openrouter": True, "ollama": False}

# List models
models = client.list_available_models()
print(models)
```

### Vector Store

#### `CodeVectorStore` Class

ChromaDB-based vector store for code and documentation.

```python
from banana_forge.vector_store import get_vector_store

# Get vector store instance
vector_store = get_vector_store()

# Index project files
stats = vector_store.index_project_files()
print(f"Indexed {stats['files_indexed']} files")

# Search for relevant code
results = vector_store.search("authentication patterns", n_results=5)
for result in results:
    print(f"File: {result['metadata']['file_path']}")
    print(f"Content: {result['content'][:100]}...")

# Get collection statistics
stats = vector_store.get_collection_stats()
print(f"Total documents: {stats['total_documents']}")
```

## Environment Variables

Configure Banana Forge using environment variables or `.env` file:

### Required
- `OPENROUTER_API_KEY`: Your OpenRouter API key

### Optional
- `OPENROUTER_BASE_URL`: OpenRouter API base URL (default: https://openrouter.ai/api/v1)
- `OLLAMA_BASE_URL`: Ollama server URL (default: http://localhost:11434)
- `OLLAMA_MODEL`: Local model name (default: qwen2.5:8b)
- `PRIMARY_MODEL`: Primary model for generation (default: moonshot/kimi-k2)
- `LOCAL_MODEL`: Local model for intermediate tasks (default: qwen2.5:8b)
- `MAX_CONCURRENT_AGENTS`: Maximum concurrent agents (default: 5)
- `CHROMA_DB_PATH`: ChromaDB storage path (default: ./chroma_db)
- `LOG_LEVEL`: Logging level (default: INFO)
- `VERBOSE`: Enable verbose output (default: false)

## Error Handling

### Common Exceptions

- `ValueError`: Configuration errors (missing API keys, invalid settings)
- `ConnectionError`: Network issues with API providers
- `TimeoutError`: Request timeouts
- `FileNotFoundError`: Missing input files
- `PermissionError`: File system permission issues

### Error Recovery

```python
from banana_forge.core import run_generation
from banana_forge.config import settings

try:
    result = run_generation("My feature")
except ValueError as e:
    if "API_KEY" in str(e):
        print("Please configure your OpenRouter API key")
    else:
        print(f"Configuration error: {e}")
except Exception as e:
    if settings.verbose:
        import traceback
        traceback.print_exc()
    print(f"Generation failed: {e}")
```

## Integration Examples

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Generate feature plan
  run: |
    uv run banana-forge generate "${{ github.event.pull_request.title }}" \
      --output feature_plan.md
  env:
    OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
```

### Pre-commit Hook

```bash
#!/bin/bash
# .git/hooks/pre-commit
if git diff --cached --name-only | grep -q "features/"; then
    echo "Generating implementation plans for new features..."
    for file in $(git diff --cached --name-only | grep "features/"); do
        feature_name=$(basename "$file" .md)
        uv run banana-forge generate "$feature_name" -o "plans/$feature_name.md"
    done
fi
```

### Python Script Integration

```python
#!/usr/bin/env python3
"""Generate plans for multiple features."""

from banana_forge.core import run_generation
import sys

features = [
    "User authentication system",
    "Payment processing integration",
    "Real-time notifications"
]

for feature in features:
    try:
        plan = run_generation(feature)
        filename = f"plans/{feature.replace(' ', '_').lower()}.md"
        with open(filename, 'w') as f:
            f.write(plan)
        print(f"Generated plan for: {feature}")
    except Exception as e:
        print(f"Failed to generate plan for {feature}: {e}")
        sys.exit(1)
```
