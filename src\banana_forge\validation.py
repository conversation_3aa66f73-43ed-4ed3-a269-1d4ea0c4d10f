"""
Output validation and quality assurance for Banana Forge.

This module provides validation functions to ensure the generated
implementation plans meet quality standards and formatting requirements.
"""

import logging
import re
from datetime import datetime

logger = logging.getLogger(__name__)


class PlanValidator:
    """
    Validates and ensures quality of generated implementation plans.

    This class checks that the generated markdown follows the expected
    structure, contains all required sections, and meets quality standards.
    """

    # Expected sections in a comprehensive implementation plan
    REQUIRED_SECTIONS = [
        "overview",
        "current state",
        "implementation",
        "testing",
        "deployment"
    ]

    # Recommended sections for comprehensive plans
    RECOMMENDED_SECTIONS = [
        "overview",
        "current state analysis",
        "reference standards",
        "implementation strategy",
        "detailed implementation plan",
        "data management",
        "user interface design",
        "integration requirements",
        "error handling",
        "performance",
        "monitoring",
        "type definitions",
        "testing strategy",
        "deployment",
        "risk assessment"
    ]

    def __init__(self):
        """Initialize the plan validator."""
        pass

    def validate_plan(self, plan_content: str, feature_description: str) -> dict[str, any]:
        """
        Validate a generated implementation plan.

        Args:
            plan_content: The generated plan content
            feature_description: Original feature description

        Returns:
            Dictionary with validation results and suggestions
        """
        validation_result = {
            "is_valid": True,
            "score": 0.0,
            "issues": [],
            "suggestions": [],
            "sections_found": [],
            "sections_missing": [],
            "word_count": 0,
            "has_code_examples": False,
            "has_proper_structure": False
        }

        try:
            # Basic content checks
            if not plan_content or not plan_content.strip():
                validation_result["is_valid"] = False
                validation_result["issues"].append("Plan content is empty")
                return validation_result

            # Word count
            validation_result["word_count"] = len(plan_content.split())

            # Check for proper markdown structure
            validation_result["has_proper_structure"] = self._check_markdown_structure(plan_content)

            # Check for required sections
            sections_found, sections_missing = self._check_sections(plan_content)
            validation_result["sections_found"] = sections_found
            validation_result["sections_missing"] = sections_missing

            # Check for code examples
            validation_result["has_code_examples"] = self._check_code_examples(plan_content)

            # Calculate quality score
            validation_result["score"] = self._calculate_quality_score(validation_result)

            # Generate suggestions
            validation_result["suggestions"] = self._generate_suggestions(validation_result)

            # Determine if plan is valid
            validation_result["is_valid"] = (
                validation_result["score"] >= 0.6 and
                len(validation_result["sections_found"]) >= len(self.REQUIRED_SECTIONS) // 2
            )

            logger.info(f"Plan validation completed: score={validation_result['score']:.2f}, valid={validation_result['is_valid']}")

        except Exception as e:
            logger.error(f"Plan validation failed: {e}")
            validation_result["is_valid"] = False
            validation_result["issues"].append(f"Validation error: {e}")

        return validation_result

    def _check_markdown_structure(self, content: str) -> bool:
        """Check if the content has proper markdown structure."""
        # Check for headings
        heading_pattern = r'^#+\s+.+$'
        headings = re.findall(heading_pattern, content, re.MULTILINE)

        # Should have at least 3 headings for a proper structure
        return len(headings) >= 3

    def _check_sections(self, content: str) -> tuple[list[str], list[str]]:
        """Check which sections are present in the content."""
        content_lower = content.lower()

        sections_found = []
        sections_missing = []

        # Check recommended sections first
        for section in self.RECOMMENDED_SECTIONS:
            section_patterns = [
                section.lower(),
                section.replace(" ", "").lower(),
                section.replace(" ", "_").lower(),
                section.replace(" ", "-").lower()
            ]

            found = any(pattern in content_lower for pattern in section_patterns)
            if found:
                sections_found.append(section)

        # Check required sections
        for section in self.REQUIRED_SECTIONS:
            section_patterns = [
                section.lower(),
                section.replace(" ", "").lower(),
                section.replace(" ", "_").lower(),
                section.replace(" ", "-").lower()
            ]

            found = any(pattern in content_lower for pattern in section_patterns)
            if not found:
                sections_missing.append(section)

        return sections_found, sections_missing

    def _check_code_examples(self, content: str) -> bool:
        """Check if the content includes code examples."""
        # Look for code blocks
        code_block_pattern = r'```[\s\S]*?```'
        inline_code_pattern = r'`[^`]+`'

        code_blocks = re.findall(code_block_pattern, content)
        inline_code = re.findall(inline_code_pattern, content)

        return len(code_blocks) > 0 or len(inline_code) > 5

    def _calculate_quality_score(self, validation_data: dict[str, any]) -> float:
        """Calculate a quality score for the plan."""
        score = 0.0

        # Word count score (0.2 weight)
        word_count = validation_data["word_count"]
        if word_count >= 1000:
            score += 0.2
        elif word_count >= 500:
            score += 0.1

        # Structure score (0.2 weight)
        if validation_data["has_proper_structure"]:
            score += 0.2

        # Sections score (0.4 weight)
        sections_found = len(validation_data["sections_found"])
        total_recommended = len(self.RECOMMENDED_SECTIONS)
        section_ratio = min(sections_found / total_recommended, 1.0)
        score += 0.4 * section_ratio

        # Code examples score (0.1 weight)
        if validation_data["has_code_examples"]:
            score += 0.1

        # Missing required sections penalty (0.1 weight)
        missing_required = len(validation_data["sections_missing"])
        if missing_required == 0:
            score += 0.1

        return min(score, 1.0)

    def _generate_suggestions(self, validation_data: dict[str, any]) -> list[str]:
        """Generate suggestions for improving the plan."""
        suggestions = []

        # Word count suggestions
        if validation_data["word_count"] < 500:
            suggestions.append("Consider adding more detail to reach at least 500 words")

        # Structure suggestions
        if not validation_data["has_proper_structure"]:
            suggestions.append("Add more section headings to improve document structure")

        # Missing sections
        if validation_data["sections_missing"]:
            missing = ", ".join(validation_data["sections_missing"])
            suggestions.append(f"Consider adding these missing sections: {missing}")

        # Code examples
        if not validation_data["has_code_examples"]:
            suggestions.append("Add code examples or snippets to illustrate implementation details")

        # Quality score
        if validation_data["score"] < 0.7:
            suggestions.append("Overall plan quality could be improved - consider adding more comprehensive details")

        return suggestions

    def fix_common_issues(self, plan_content: str, feature_description: str) -> str:
        """
        Automatically fix common issues in the generated plan.

        Args:
            plan_content: The original plan content
            feature_description: Original feature description

        Returns:
            Fixed plan content
        """
        try:
            fixed_content = plan_content

            # Ensure proper title
            if not fixed_content.strip().startswith("#"):
                fixed_content = f"# {feature_description} Implementation Plan\n\n{fixed_content}"

            # Fix heading levels (ensure proper hierarchy)
            fixed_content = self._fix_heading_hierarchy(fixed_content)

            # Add timestamp if missing
            if "Generated by Banana Forge" not in fixed_content:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                footer = f"\n\n---\n*Generated by Banana Forge on {timestamp}*\n"
                fixed_content += footer

            # Ensure proper spacing
            fixed_content = re.sub(r'\n{3,}', '\n\n', fixed_content)  # Max 2 consecutive newlines
            fixed_content = fixed_content.strip() + '\n'  # Single newline at end

            logger.info("Applied automatic fixes to plan content")
            return fixed_content

        except Exception as e:
            logger.error(f"Failed to fix plan issues: {e}")
            return plan_content

    def _fix_heading_hierarchy(self, content: str) -> str:
        """Fix markdown heading hierarchy to ensure proper structure."""
        lines = content.split('\n')
        fixed_lines = []

        for line in lines:
            # Check if line is a heading
            if line.strip().startswith('#'):
                # Ensure proper spacing around headings
                if fixed_lines and fixed_lines[-1].strip():
                    fixed_lines.append('')  # Add blank line before heading
                fixed_lines.append(line)
                fixed_lines.append('')  # Add blank line after heading
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)


# Global validator instance
validator = PlanValidator()


def validate_implementation_plan(plan_content: str, feature_description: str) -> dict[str, any]:
    """
    Validate an implementation plan and return validation results.

    Args:
        plan_content: The generated plan content
        feature_description: Original feature description

    Returns:
        Dictionary with validation results
    """
    return validator.validate_plan(plan_content, feature_description)


def fix_plan_issues(plan_content: str, feature_description: str) -> str:
    """
    Automatically fix common issues in a generated plan.

    Args:
        plan_content: The original plan content
        feature_description: Original feature description

    Returns:
        Fixed plan content
    """
    return validator.fix_common_issues(plan_content, feature_description)
