"""
Supervisor agent for orchestrating the multi-agent system.

This module contains the supervisor agent that coordinates the 10 specialized
agents and manages the overall feature implementation planning workflow.
"""

import logging
from typing import Annotated, Any

from langchain_core.language_models import BaseLanguageModel
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.graph import MessagesState
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command

logger = logging.getLogger(__name__)


def create_handoff_tools(agent_names: list[str]) -> list[Any]:
    """
    Create handoff tools for transferring control to specialized agents.

    Args:
        agent_names: List of agent names to create handoff tools for

    Returns:
        List of handoff tool functions
    """
    tools = []

    # Agent descriptions for better supervisor decision making
    agent_descriptions = {
        "core_agent": "Handles core implementation logic and algorithms",
        "primary_ops_agent": "Manages primary user workflows and operations",
        "alt_flows_agent": "Analyzes edge cases and alternative flows",
        "error_agent": "Designs error handling and recovery mechanisms",
        "concurrency_agent": "Handles concurrency and performance optimization",
        "data_agent": "Manages data storage, models, and persistence",
        "ui_agent": "Designs user interfaces and experience",
        "integration_agent": "Handles external API integrations and services",
        "monitoring_agent": "Designs logging, monitoring, and observability",
        "types_agent": "Defines types, interfaces, and contracts",
    }

    for agent_name in agent_names:
        description = agent_descriptions.get(agent_name, f"Transfer to {agent_name}")

        @tool(
            f"transfer_to_{agent_name}",
            description=f"Transfer to {agent_name}: {description}",
        )
        def create_handoff_tool(
            task_description: Annotated[str, "Specific task for the agent to complete"],
            state: Annotated[MessagesState, InjectedState],
            tool_call_id: Annotated[str, InjectedToolCallId],
            agent_name=agent_name,  # Capture agent_name in closure
        ) -> Command:
            """Create a handoff command to transfer control to a specialized agent."""
            tool_message = {
                "role": "tool",
                "content": f"Successfully transferred to {agent_name} with task: "
                f"{task_description}",
                "name": f"transfer_to_{agent_name}",
                "tool_call_id": tool_call_id,
            }

            # Create a focused message for the agent (not used in current
            # implementation)
            # agent_message = {
            #     "role": "user",
            #     "content": f"Task: {task_description}\n\nContext: "
            #     f"{state['messages'][-1].content if state['messages'] else "
            #     f"'No additional context'}"
            # }

            return Command(
                goto=agent_name,
                update={
                    "messages": state["messages"] + [tool_message],
                    "agent_task": task_description,
                    "current_agent": agent_name,
                },
                graph=Command.PARENT,
            )

        tools.append(create_handoff_tool)

    return tools


def create_supervisor_agent(
    primary_model: BaseLanguageModel, agent_names: list[str]
) -> Any:
    """
    Create the supervisor agent that orchestrates the specialized agents.

    Args:
        primary_model: The primary LLM for supervisor reasoning
        agent_names: List of specialized agent names

    Returns:
        Configured supervisor agent
    """
    # Create handoff tools for all specialized agents
    handoff_tools = create_handoff_tools(agent_names)

    supervisor_prompt = (
        f"You are a Supervisor Agent orchestrating a team of {len(agent_names)} "
        f"specialized agents for feature implementation planning.\n\n"
        f"AVAILABLE AGENTS:\n"
        f"- core_agent: Core implementation logic and algorithms\n"
        f"- primary_ops_agent: Primary user workflows and operations\n"
        f"- alt_flows_agent: Edge cases and alternative flows\n"
        f"- error_agent: Error handling and recovery mechanisms\n"
        f"- concurrency_agent: Concurrency and performance optimization\n"
        f"- data_agent: Data storage, models, and persistence\n"
        f"- ui_agent: User interfaces and experience design\n"
        f"- integration_agent: External API integrations and services\n"
        f"- monitoring_agent: Logging, monitoring, and observability\n"
        f"- types_agent: Types, interfaces, and contracts\n\n"
        f"ORCHESTRATION STRATEGY:\n"
        f"1. Analyze the feature request to understand scope and requirements\n"
        f"2. Identify which aspects need investigation (typically 3-5 agents for "
        f"most features)\n"
        f"3. Delegate specific, focused tasks to relevant agents\n"
        f"4. Review agent responses and identify any gaps or insufficient information\n"
        f"5. If needed, delegate follow-up tasks to gather missing information "
        f"(max 2 iterations)\n"
        f"6. Collect and synthesize agent findings into a comprehensive plan\n"
        f"7. Ensure all critical aspects are covered\n\n"
        f"DELEGATION PRINCIPLES:\n"
        f"- Assign specific, actionable tasks to each agent\n"
        f"- Provide clear context about what you need from each agent\n"
        f"- Don't assign the same task to multiple agents\n"
        f"- Focus on the most relevant agents for the feature type\n"
        f"- Gather information systematically before synthesis\n\n"
        f"TASK ASSIGNMENT EXAMPLES:\n"
        f'- "Analyze the core authentication logic needed for OAuth login '
        f'implementation"\n'
        f'- "Research existing UI patterns for user registration flows"\n'
        f'- "Identify error handling requirements for payment processing failures"\n'
        f'- "Design data models for user profile management"\n\n'
        f"ITERATION GUIDELINES:\n"
        f"- After receiving agent responses, evaluate if the information is "
        f"sufficient\n"
        f"- If critical information is missing, assign follow-up tasks to "
        f"relevant agents\n"
        f"- Limit to maximum 2 iterations to avoid excessive back-and-forth\n"
        f"- Focus follow-up tasks on specific gaps or unclear areas\n"
        f"- Once sufficient information is gathered, proceed to final synthesis\n\n"
        f"Your goal is to orchestrate a comprehensive analysis that covers all "
        f"necessary "
        f"aspects of the feature implementation."
    )

    supervisor = create_react_agent(
        model=primary_model,
        tools=handoff_tools,
        prompt=supervisor_prompt,
        name="supervisor",
    )

    logger.info(f"Created supervisor agent with {len(handoff_tools)} handoff tools")
    return supervisor
