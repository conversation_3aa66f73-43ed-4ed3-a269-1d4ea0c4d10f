# Auto detect text files and perform LF normalization
* text=auto eol=lf

# Explicitly set line endings for common text files
*.py text eol=lf
*.md text eol=lf
*.txt text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.json text eol=lf
*.toml text eol=lf
*.cfg text eol=lf
*.ini text eol=lf
*.sh text eol=lf

# Ensure these files are always treated as text with LF
.gitattributes text eol=lf
.gitignore text eol=lf
