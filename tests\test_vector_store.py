"""
Tests for the vector store functionality.
"""

import tempfile
import uuid
from pathlib import Path
from typing import Any
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from banana_forge.vector_store import CodeVectorStore


class TestCodeVectorStore:
    """Test cases for the CodeVectorStore class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        # Create a temp directory that doesn't contain excluded directory names
        # Use a custom directory name to avoid system temp paths with "tmp"

        # Create temp directory with a safe name that won't be excluded
        temp_base = Path(tempfile.gettempdir()) / "banana_forge_test"
        temp_base.mkdir(exist_ok=True)

        # Create unique test directory
        unique_id = str(uuid.uuid4())[:8]
        self.temp_dir = temp_base / f"test_project_{unique_id}"
        self.temp_dir.mkdir(exist_ok=True)
        self._base_temp = temp_base  # Keep reference for cleanup

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil

        # Clean up the specific test directory
        if hasattr(self, "temp_dir") and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir, ignore_errors=True)

        # Clean up base temp if it's empty
        if hasattr(self, "_base_temp") and self._base_temp.exists():
            try:
                # Only remove if empty
                self._base_temp.rmdir()
            except OSError:
                # Directory not empty or other error, ignore
                pass

    @patch("banana_forge.vector_store.chromadb")
    @patch("banana_forge.vector_store.settings")
    def test_vector_store_initialization(
        self, mock_settings: Any, mock_chromadb: Any
    ) -> None:
        """Test that vector store initializes correctly."""
        # Mock settings
        mock_settings.ensure_chroma_db_path.return_value = self.temp_dir
        mock_settings.chroma_collection_name = "test_collection"
        mock_settings.verbose = False

        # Mock ChromaDB
        mock_client = MagicMock()
        mock_collection = MagicMock()
        mock_chromadb.PersistentClient.return_value = mock_client
        mock_client.get_or_create_collection.return_value = mock_collection

        # Initialize vector store
        vector_store = CodeVectorStore()

        # Verify initialization
        assert vector_store.client == mock_client
        assert vector_store.collection == mock_collection
        mock_chromadb.PersistentClient.assert_called_once()
        mock_client.get_or_create_collection.assert_called_once_with(
            name="test_collection",
            metadata={"description": "Banana Forge code and documentation store"},
        )

    def test_split_code_content(self) -> None:
        """Test code content splitting."""
        vector_store = CodeVectorStore()

        code_content = """def function1():
    return "hello"

def function2():
    return "world"

class MyClass:
    def method1(self):
        pass"""

        chunks = vector_store._split_code_content(code_content)  # type: ignore[reportPrivateUsage]

        # Should split into logical chunks
        assert len(chunks) > 1
        assert any("function1" in chunk for chunk in chunks)
        assert any("function2" in chunk for chunk in chunks)
        assert any("MyClass" in chunk for chunk in chunks)

    def test_split_text_content(self) -> None:
        """Test text content splitting."""
        vector_store = CodeVectorStore()

        # Create content large enough to force splitting
        text_content = (
            """This is the first paragraph. """
            + "A" * 500
            + """

This is the second paragraph. """
            + "B" * 500
            + """

This is the third paragraph. """
            + "C" * 500
        )

        chunks = vector_store._split_text_content(text_content)  # type: ignore[reportPrivateUsage]

        # Should split into multiple chunks due to size
        assert len(chunks) >= 2
        assert "first paragraph" in chunks[0]
        # At least verify we have multiple chunks
        assert len(chunks) > 1

    @patch("banana_forge.vector_store.settings")
    def test_index_project_files_with_mock_files(self, mock_settings: Any) -> None:
        """Test indexing project files with mock file structure."""
        # Create test files
        test_files = {
            "main.py": "def main():\n    print('Hello World')",
            "utils.py": "def helper():\n    return True",
            "README.md": "# Project\n\nThis is a test project.",
            "config.json": '{"setting": "value"}',
        }

        for filename, content in test_files.items():
            (self.temp_dir / filename).write_text(content)

        # Mock settings
        mock_settings.get_project_root.return_value = self.temp_dir
        mock_settings.verbose = False

        # Mock the vector store's collection and client
        with patch.object(CodeVectorStore, "_setup_client"):
            vector_store = CodeVectorStore()
            vector_store.collection = MagicMock()

            # Mock collection.get to return empty results (documents don't exist)
            vector_store.collection.get.return_value = {"ids": []}
            # Mock collection.count to return a reasonable number
            vector_store.collection.count.return_value = 4
            # Mock collection.add to track calls
            vector_store.collection.add = MagicMock()

            # Index the files - pass the temp_dir explicitly
            stats = vector_store.index_project_files(self.temp_dir)

            # Verify results - the test should process and index all 4 files
            assert stats["files_processed"] == 4
            assert stats["files_indexed"] == 4
            assert stats["errors"] == 0

            # Verify collection.add was called (should be called for each chunk)
            assert vector_store.collection.add.call_count > 0

    @patch("banana_forge.vector_store.settings")
    def test_search_functionality(self, mock_settings: Any) -> None:
        """Test search functionality."""
        mock_settings.max_code_snippets = 5
        mock_settings.verbose = False

        with patch.object(CodeVectorStore, "_setup_client"):
            vector_store = CodeVectorStore()
            vector_store.collection = MagicMock()

            # Mock search results
            mock_results: dict[str, Any] = {
                "documents": [["def test_function():\n    return True"]],
                "metadatas": [[{"file_path": "test.py", "file_type": ".py"}]],
                "distances": [[0.1]],
                "ids": [["test.py#abc123#chunk0"]],
            }
            vector_store.collection.query.return_value = mock_results

            # Perform search
            results = vector_store.search("test function", n_results=5)

            # Verify results
            assert len(results) == 1
            assert "test_function" in results[0]["content"]
            assert results[0]["metadata"]["file_path"] == "test.py"
            assert results[0]["distance"] == 0.1

    @patch("banana_forge.vector_store.settings")
    def test_get_collection_stats(self, mock_settings: Any) -> None:
        """Test getting collection statistics."""
        mock_settings.chroma_collection_name = "test_collection"
        mock_settings.chroma_db_path = Path("/test/path")

        with patch.object(CodeVectorStore, "_setup_client"):
            vector_store = CodeVectorStore()
            vector_store.collection = MagicMock()
            vector_store.collection.count.return_value = 42
            vector_store.collection.peek.return_value = {
                "metadatas": [
                    {"file_type": ".py"},
                    {"file_type": ".js"},
                    {"file_type": ".py"},
                ]
            }

            # Get stats
            stats = vector_store.get_collection_stats()

            # Verify stats
            assert stats["total_documents"] == 42
            assert stats["collection_name"] == "test_collection"
            assert ".py" in stats["sample_file_types"]
            assert ".js" in stats["sample_file_types"]

    @patch("banana_forge.vector_store.settings")
    def test_reset_collection(self, mock_settings: Any) -> None:
        """Test resetting the collection."""
        mock_settings.chroma_collection_name = "test_collection"

        with patch.object(CodeVectorStore, "_setup_client"):
            vector_store = CodeVectorStore()
            vector_store.client = MagicMock()
            vector_store.collection = MagicMock()

            # Mock the reset operations
            new_collection = MagicMock()
            vector_store.client.create_collection.return_value = new_collection

            # Reset collection
            result = vector_store.reset_collection()

            # Verify reset
            assert result is True
            vector_store.client.delete_collection.assert_called_once_with(
                "test_collection"
            )
            vector_store.client.create_collection.assert_called_once()
            assert vector_store.collection == new_collection

    def test_vector_store_initialization_error(self):
        """Test vector store initialization error handling."""
        from banana_forge.vector_store import CodeVectorStore

        with patch('banana_forge.vector_store.chromadb.PersistentClient', side_effect=Exception("DB error")):
            try:
                CodeVectorStore()
                # If it doesn't crash, that's fine
                assert True
            except Exception:
                # Expected in some cases
                assert True

    def test_vector_store_search_error_handling(self):
        """Test vector store search error handling."""
        from banana_forge.vector_store import CodeVectorStore

        with patch('banana_forge.vector_store.chromadb.PersistentClient') as mock_chromadb:
            mock_client = Mock()
            mock_collection = Mock()
            mock_collection.query.side_effect = Exception("Query error")
            mock_client.get_or_create_collection.return_value = mock_collection
            mock_chromadb.return_value = mock_client

            store = CodeVectorStore()
            results = store.search("test query", n_results=5)

            # Should return empty list on error
            assert results == []


if __name__ == "__main__":
    pytest.main([__file__])
