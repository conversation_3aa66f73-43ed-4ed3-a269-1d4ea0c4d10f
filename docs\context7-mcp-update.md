# Context7 MCP Server Integration Update

**Date:** July 18, 2025  
**Version:** 0.2.1  
**Status:** ✅ COMPLETE

## Summary

Updated Banana Forge's Context7 integration to use the official Context7 MCP (Model Context Protocol) server instead of the private preview API. This provides better reliability, up-to-date documentation access, and eliminates the need for API keys.

## Changes Made

### ✅ Context7 Tool Rewrite

**File:** `src/banana_forge/tools/context7_tool.py`

- **Removed**: HTTP API-based implementation
- **Added**: MCP server communication via subprocess
- **Added**: Automatic availability detection
- **Added**: Helpful error messages with setup instructions

### ✅ MCP Communication Protocol

The tool now uses the Context7 MCP server with two main operations:

1. **`resolve-library-id`**: Converts library names to Context7-compatible IDs
2. **`get-library-docs`**: Fetches documentation using the resolved ID

### ✅ Graceful Degradation

- **Availability Check**: Detects if Node.js/npx is available
- **Helpful Messages**: Provides setup instructions when MCP server is unavailable
- **No Breaking Changes**: System continues to work without Context7

### ✅ Updated Configuration

**File:** `src/banana_forge/config.py`

- Marked old API settings as deprecated
- Added notes about MCP server approach
- Maintained backward compatibility

### ✅ Updated Documentation

**Files:**
- `docs/phase-2-multi-agent-guide.md`
- `docs/context7-mcp-update.md` (this file)

- Added Context7 MCP setup section
- Updated configuration examples
- Added troubleshooting for Node.js requirements

### ✅ Updated Tests

**File:** `tests/test_tools.py`

- Updated Context7 tests to use MCP mocking
- Added availability check tests
- Maintained test coverage

## Technical Implementation

### MCP Communication Flow

```
1. Check if npx is available
2. Resolve library name → Context7 ID
   └── npx @upstash/context7-mcp (resolve-library-id)
3. Fetch documentation using ID
   └── npx @upstash/context7-mcp (get-library-docs)
4. Format and return results
```

### Example Usage

```python
from banana_forge.tools.context7_tool import create_context7_tool

tool = create_context7_tool()

# Get FastAPI authentication docs
result = tool._run(
    library_name="fastapi",
    topic="authentication", 
    tokens=10000
)
```

### Error Handling

The tool provides helpful error messages:

```
Context7 MCP server is not available. To use Context7 documentation:
1. Install Node.js (https://nodejs.org/)
2. The Context7 MCP server will be automatically installed when needed
3. Alternatively, install manually: npm install -g @upstash/context7-mcp

For now, try searching for 'fastapi' documentation manually or use web search.
```

## Prerequisites

### Required
- **Node.js 18+**: For running the MCP server
- **NPX**: Comes with Node.js, used to execute the MCP server

### Optional
- **Global Installation**: `npm install -g @upstash/context7-mcp`

## Benefits

### ✅ No API Keys Required
- Context7 MCP server is free and open source
- No need to manage API keys or rate limits

### ✅ Up-to-Date Documentation
- Direct access to latest library documentation
- Community-maintained and regularly updated

### ✅ Better Integration
- Uses standard MCP protocol
- More reliable than HTTP API calls

### ✅ Automatic Installation
- MCP server is installed automatically when needed
- No manual setup required for most users

## Migration Guide

### For Users

**No action required** - the system automatically uses the new MCP approach.

If you want to ensure Context7 works optimally:

1. **Install Node.js**: Download from [nodejs.org](https://nodejs.org/)
2. **Verify Installation**: Run `npx --version`
3. **Optional**: Pre-install MCP server: `npm install -g @upstash/context7-mcp`

### For Developers

**Configuration Changes:**
- Remove `CONTEXT7_API_URL` and `CONTEXT7_API_KEY` from environment
- No new configuration needed

**Code Changes:**
- No changes needed - tool interface remains the same
- Context7Tool continues to work with same parameters

## Testing

### Manual Testing

```bash
# Test Node.js availability
npx --version

# Test Context7 MCP server
npx -y @upstash/context7-mcp --help

# Test in Banana Forge
uv run python -c "
from src.banana_forge.tools.context7_tool import create_context7_tool
tool = create_context7_tool()
print(tool._run('fastapi', 'authentication', 5000))
"
```

### Automated Testing

All existing tests continue to pass with updated mocking for MCP communication.

## Troubleshooting

### Node.js Not Found

**Error**: `Context7 MCP server is not available`

**Solution**:
1. Install Node.js from [nodejs.org](https://nodejs.org/)
2. Restart your terminal/IDE
3. Verify with `npx --version`

### MCP Server Timeout

**Error**: `Context7 MCP server timed out`

**Solution**:
1. Check internet connection
2. Try manual installation: `npm install -g @upstash/context7-mcp`
3. Increase timeout in code if needed

### Library Not Found

**Error**: `Could not find library 'xyz' in Context7`

**Solution**:
1. Try a more specific library name
2. Check [Context7 supported libraries](https://context7.com)
3. Use web search tool as fallback

## Future Enhancements

### Potential Improvements

1. **Caching**: Cache MCP responses to improve performance
2. **Batch Requests**: Support multiple library queries in one call
3. **Custom Libraries**: Support for private/custom library documentation
4. **Async Support**: Full async implementation for better performance

### Integration Opportunities

1. **IDE Integration**: Direct MCP server integration in development environments
2. **CI/CD**: Documentation validation in build pipelines
3. **Team Sharing**: Shared MCP server instances for teams

## Conclusion

The Context7 MCP integration provides a more robust, reliable, and user-friendly way to access up-to-date library documentation. The migration maintains full backward compatibility while offering significant improvements in reliability and ease of use.

**Key Benefits:**
- ✅ No API keys required
- ✅ More reliable documentation access
- ✅ Automatic installation and setup
- ✅ Better error handling and user guidance
- ✅ Full backward compatibility

The system is now ready for production use with enhanced documentation capabilities.
