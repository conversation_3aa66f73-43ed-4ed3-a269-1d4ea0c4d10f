"""
Agent output compression and summarization for Banana Forge.

This module handles compressing and summarizing agent outputs to manage
token limits and improve the quality of the final synthesis.
"""

import logging
from typing import Any

from ..config import settings
from ..llm import LLMClient

logger = logging.getLogger(__name__)


class AgentOutputCompressor:
    """
    Compresses and summarizes agent outputs for efficient final synthesis.

    This class uses the local model to compress verbose agent outputs into
    concise, focused summaries that preserve key insights while reducing
    token usage for the final synthesis step.
    """

    def __init__(self):
        """Initialize the output compressor."""
        self.llm_client = LLMClient()

    def compress_agent_output(
        self,
        agent_name: str,
        agent_output: str,
        task_description: str,
        max_length: int = 500,
    ) -> str:
        """
        Compress a single agent's output into a concise summary.

        Args:
            agent_name: Name of the agent that produced the output
            agent_output: The full output from the agent
            task_description: The task that was assigned to the agent
            max_length: Maximum length of the compressed output in tokens

        Returns:
            Compressed and summarized output
        """
        try:
            if not agent_output or not agent_output.strip():
                return f"[{agent_name}] No output generated"

            # If output is already short, return as-is
            if len(agent_output) <= max_length * 4:  # Rough token estimation
                return f"[{agent_name}] {agent_output.strip()}"

            compression_prompt = (
                f"You are an expert at summarizing technical analysis.\n"
                f"Compress the following agent output into a concise summary that "
                f"preserves all key insights and recommendations.\n\n"
                f"AGENT: {agent_name}\n"
                f"TASK: {task_description}\n\n"
                f"AGENT OUTPUT:\n{agent_output}\n\n"
                f"COMPRESSION INSTRUCTIONS:\n"
                f"- Preserve all specific technical recommendations and findings\n"
                f"- Keep important code snippets, patterns, or examples mentioned\n"
                f"- Maintain any specific file names, functions, or technical details\n"
                f"- Focus on actionable insights and concrete recommendations\n"
                f"- Remove verbose explanations but keep the core substance\n"
                f"- Aim for {max_length} words or less\n"
                f"- Use bullet points for clarity when appropriate\n\n"
                f"COMPRESSED SUMMARY:"
            )

            compressed = self.llm_client.generate_completion(
                prompt=compression_prompt,
                model=settings.local_model,
                max_tokens=max_length,
                temperature=0.3,  # Lower temperature for more focused compression
            )

            # Add agent identifier
            result = f"[{agent_name}] {compressed.strip()}"

            if settings.verbose:
                original_length = len(agent_output)
                compressed_length = len(result)
                compression_ratio = (
                    compressed_length / original_length if original_length > 0 else 0
                )
                logger.info(
                    f"Compressed {agent_name} output: {original_length} -> "
                    f"{compressed_length} chars (ratio: {compression_ratio:.2f})"
                )

            return result

        except Exception as e:
            logger.error(f"Failed to compress output from {agent_name}: {e}")
            # Return a truncated version as fallback
            truncated = (
                agent_output[: max_length * 4]
                + "... [truncated due to compression error]"
            )
            return f"[{agent_name}] {truncated}"

    def compress_multiple_outputs(
        self, agent_outputs: dict[str, dict[str, Any]], max_length_per_agent: int = 500
    ) -> dict[str, str]:
        """
        Compress outputs from multiple agents.

        Args:
            agent_outputs: Dictionary mapping agent names to their output data
            max_length_per_agent: Maximum length per agent output

        Returns:
            Dictionary mapping agent names to compressed outputs
        """
        compressed_outputs = {}

        for agent_name, output_data in agent_outputs.items():
            try:
                agent_output = output_data.get("output", "")
                task_description = output_data.get("task", f"Analysis by {agent_name}")

                compressed = self.compress_agent_output(
                    agent_name=agent_name,
                    agent_output=agent_output,
                    task_description=task_description,
                    max_length=max_length_per_agent,
                )

                compressed_outputs[agent_name] = compressed

            except Exception as e:
                logger.error(f"Failed to compress output from {agent_name}: {e}")
                compressed_outputs[agent_name] = (
                    f"[{agent_name}] Error compressing output: {e}"
                )

        return compressed_outputs

    def create_synthesis_prompt(
        self,
        feature_description: str,
        additional_context: str,
        compressed_outputs: dict[str, str],
    ) -> str:
        """
        Create a synthesis prompt for the final model using compressed agent outputs.

        Args:
            feature_description: Original feature description
            additional_context: Additional context provided
            compressed_outputs: Compressed outputs from all agents

        Returns:
            Formatted prompt for final synthesis
        """
        # Group outputs by category for better organization
        categorized_outputs = self._categorize_agent_outputs(compressed_outputs)

        prompt_parts = [
            "# Feature Implementation Plan Synthesis",
            "",
            f"**Feature:** {feature_description}",
            f"**Additional Context:** "
            f"{additional_context if additional_context else 'None provided'}",
            "",
            "You are an expert software architect tasked with synthesizing "
            "comprehensive feature implementation plans.",
            f"Below are analysis results from {len(compressed_outputs)} specialized "
            f"agents who have analyzed different aspects of this feature.",
            "",
            "## Agent Analysis Results",
            "",
        ]

        # Add categorized outputs
        for category, outputs in categorized_outputs.items():
            if outputs:
                prompt_parts.append(f"### {category}")
                for output in outputs:
                    prompt_parts.append(output)
                prompt_parts.append("")

        # Add synthesis instructions
        prompt_parts.extend(
            [
                "## Synthesis Instructions",
                "",
                "Create a comprehensive feature implementation plan that integrates "
                "all agent findings into a cohesive, actionable document.",
                "Follow this structure:",
                "",
                "1. **Overview** - High-level summary and business value",
                "2. **Current State Analysis** - Existing codebase and relevant "
                "components",
                "3. **Reference Standards** - Coding standards and best practices "
                "to follow",
                "4. **Implementation Strategy** - Core approach and architecture "
                "decisions",
                "5. **Detailed Implementation Plan** - Step-by-step implementation "
                "phases",
                "6. **Data Management** - Database schemas, models, and persistence",
                "7. **User Interface Design** - UI/UX considerations and components",
                "8. **Integration Requirements** - External APIs and service "
                "integrations",
                "9. **Error Handling & Edge Cases** - Comprehensive error management",
                "10. **Performance & Concurrency** - Optimization and scalability "
                "considerations",
                "11. **Monitoring & Observability** - Logging, metrics, and debugging",
                "12. **Type Definitions** - Interfaces, contracts, and type safety",
                "13. **Testing Strategy** - Unit, integration, and end-to-end testing",
                "14. **Deployment & Rollout** - Deployment strategy and rollback plans",
                "15. **Risk Assessment** - Potential risks and mitigation strategies",
                "",
                "Ensure the plan is:",
                "- Specific and actionable with concrete steps",
                "- Technically accurate and feasible",
                "- Well-organized and easy to follow",
                "- Comprehensive but focused on the most important aspects",
                "- Includes relevant code examples or patterns where helpful",
                "",
                "Generate the complete implementation plan now:",
            ]
        )

        return "\n".join(prompt_parts)

    def _categorize_agent_outputs(
        self, compressed_outputs: dict[str, str]
    ) -> dict[str, list[str]]:
        """Categorize agent outputs for better organization in the synthesis prompt."""
        categories = {
            "Core Implementation & Architecture": [],
            "User Experience & Interfaces": [],
            "Data & Integration": [],
            "Quality & Operations": [],
        }

        # Map agents to categories
        agent_categories = {
            "core_agent": "Core Implementation & Architecture",
            "primary_ops_agent": "User Experience & Interfaces",
            "alt_flows_agent": "User Experience & Interfaces",
            "concurrency_agent": "Core Implementation & Architecture",
            "data_agent": "Data & Integration",
            "ui_agent": "User Experience & Interfaces",
            "integration_agent": "Data & Integration",
            "error_agent": "Quality & Operations",
            "monitoring_agent": "Quality & Operations",
            "types_agent": "Core Implementation & Architecture",
        }

        for agent_name, output in compressed_outputs.items():
            category = agent_categories.get(agent_name, "Quality & Operations")
            categories[category].append(output)

        return categories
