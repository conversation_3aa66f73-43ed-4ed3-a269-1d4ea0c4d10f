"""Tests for package initialization."""


class TestPackageInit:
    """Test cases for package initialization."""

    def test_main_package_import(self):
        """Test that the main package can be imported."""
        import banana_forge

        assert banana_forge is not None

    def test_agents_package_import(self):
        """Test that the agents package can be imported."""
        import banana_forge.agents

        assert banana_forge.agents is not None

    def test_tools_package_import(self):
        """Test that the tools package can be imported."""
        import banana_forge.tools

        assert banana_forge.tools is not None

    def test_package_version(self):
        """Test that package has version information."""
        import banana_forge

        # Should have some version-related attribute or not crash
        try:
            version = getattr(banana_forge, "__version__", None)
            # If version exists, it should be a string
            if version is not None:
                assert isinstance(version, str)
            assert True  # Test passes if we get here
        except Exception:
            # If version info is not available, that's fine
            assert True

    def test_main_module_attributes(self):
        """Test main module attributes."""
        import banana_forge

        # Should be a module
        assert hasattr(banana_forge, "__name__")
        assert banana_forge.__name__ == "banana_forge"

    def test_submodule_imports(self):
        """Test that key submodules can be imported."""
        try:
            from banana_forge import config

            assert config is not None
        except ImportError:
            # If import fails, that's fine for this test
            pass

        try:
            from banana_forge import core

            assert core is not None
        except ImportError:
            # If import fails, that's fine for this test
            pass

    def test_main_function_exists(self):
        """Test that main function exists."""
        from banana_forge import main

        assert callable(main)

    def test_package_exports(self):
        """Test package exports."""
        import banana_forge

        # Check for expected exports
        expected_exports = ["main", "run_generation", "settings"]
        for export in expected_exports:
            try:
                assert hasattr(banana_forge, export)
            except (AttributeError, ImportError):
                # Some exports might not be available in test environment
                pass
