"""
Configuration management for Banana Forge.

This module handles all configuration settings using Pydantic BaseSettings,
allowing configuration via environment variables or .env files.
"""

from pathlib import Path

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Configuration settings for Banana Forge."""

    # OpenRouter API Configuration
    openrouter_api_key: str | None = Field(
        default=None, description="API key for OpenRouter service"
    )
    openrouter_base_url: str = Field(
        default="https://openrouter.ai/api/v1",
        description="Base URL for OpenRouter API",
    )

    # Google Gemini API Configuration
    gemini_api_key: str | None = Field(
        default=None, description="API key for Google Gemini service"
    )
    gemini_base_url: str = Field(
        default="https://generativelanguage.googleapis.com/v1beta",
        description="Base URL for Google Gemini API",
    )

    # Context7 MCP Configuration (optional)
    context7_api_url: str | None = Field(
        default=None, description="API URL for Context7 documentation service"
    )
    context7_api_key: str | None = Field(
        default=None,
        description="API key for Context7 service (deprecated - now uses MCP server)",
    )
    context7_api_url: str = Field(
        default="",
        description="URL for Context7 documentation service (deprecated - now uses MCP server)",
    )
    web_search_api_key: str = Field(
        default="", description="API key for web search service (SerpAPI, etc.)"
    )

    # Ollama Configuration (for local LLM)
    ollama_base_url: str = Field(
        default="http://localhost:11434", description="Base URL for Ollama server"
    )
    ollama_model: str = Field(
        default="qwen2.5:8b", description="Local model name for Ollama"
    )

    # ChromaDB Configuration
    chroma_db_path: Path = Field(
        default=Path("./chroma_db"), description="Path to ChromaDB storage directory"
    )
    chroma_collection_name: str = Field(
        default="banana_forge_code", description="Name of the ChromaDB collection"
    )

    # Logging Configuration
    log_level: str = Field(
        default="INFO", description="Logging level (DEBUG, INFO, WARNING, ERROR)"
    )
    verbose: bool = Field(default=False, description="Enable verbose output")

    # Model Configuration
    primary_model: str = Field(
        default="moonshotai/kimi-k2",
        description="Primary model for final synthesis (via OpenRouter)",
    )
    local_model: str = Field(
        default="qwen2.5:8b", description="Local model for intermediate tasks"
    )
    max_concurrent_agents: int = Field(
        default=5, description="Maximum number of concurrent agents"
    )
    max_iterations: int = Field(
        default=2, description="Maximum iterations for agent refinement"
    )

    # Performance Configuration
    max_code_snippets: int = Field(
        default=5, description="Maximum number of code snippets to include"
    )
    max_doc_snippets: int = Field(
        default=3, description="Maximum number of documentation snippets to include"
    )
    context_window_size: int = Field(
        default=128000, description="Context window size for the primary model"
    )

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore",
    }

    def validate_required_settings(self) -> None:
        """Validate that required settings are present."""
        if not self.openrouter_api_key:
            raise ValueError(
                "OPENROUTER_API_KEY is required. Please set it in your .env file "
                "or environment variables."
            )

    def get_project_root(self) -> Path:
        """Get the project root directory."""
        # Try to find the project root by looking for pyproject.toml
        current = Path.cwd()
        while current != current.parent:
            if (current / "pyproject.toml").exists():
                return current
            current = current.parent
        return Path.cwd()

    def ensure_chroma_db_path(self) -> Path:
        """Ensure the ChromaDB path exists and return it."""
        if not self.chroma_db_path.is_absolute():
            # Make it relative to project root
            self.chroma_db_path = self.get_project_root() / self.chroma_db_path

        self.chroma_db_path.mkdir(parents=True, exist_ok=True)
        return self.chroma_db_path


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def validate_environment() -> bool:
    """
    Validate that the environment is properly configured.

    Returns:
        bool: True if environment is valid, False otherwise
    """
    try:
        settings.validate_required_settings()
        return True
    except ValueError as e:
        print(f"Configuration error: {e}")
        return False


def setup_logging() -> None:
    """Set up logging based on configuration."""
    import logging

    level = getattr(logging, settings.log_level.upper(), logging.INFO)

    # Configure logging format
    format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    if settings.verbose:
        format_string = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(filename)s:%(lineno)d - %(message)s"
        )

    logging.basicConfig(level=level, format=format_string, datefmt="%Y-%m-%d %H:%M:%S")

    # Set specific loggers
    if not settings.verbose:
        # Reduce noise from third-party libraries
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("openai").setLevel(logging.WARNING)
        logging.getLogger("chromadb").setLevel(logging.WARNING)
