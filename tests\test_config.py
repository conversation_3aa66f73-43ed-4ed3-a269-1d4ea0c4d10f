"""Tests for configuration functionality."""

import os
from unittest.mock import patch

from banana_forge.config import get_settings, settings


class TestConfig:
    """Test cases for configuration."""

    def test_settings_initialization(self):
        """Test settings initialization."""
        assert settings is not None
        assert hasattr(settings, 'max_concurrent_agents')
        assert hasattr(settings, 'web_search_api_key')

    def test_get_settings_function(self):
        """Test get_settings function."""
        result = get_settings()
        assert result is not None
        assert result == settings

    @patch.dict(os.environ, {'BANANA_FORGE_MAX_AGENTS': '5'})
    def test_settings_from_environment(self):
        """Test settings loading from environment variables."""
        # Reload settings to pick up environment variable
        from banana_forge.config import Settings
        test_settings = Settings()

        # The environment variable should be picked up
        assert test_settings.max_concurrent_agents >= 1

    def test_settings_validation(self):
        """Test settings validation."""
        from banana_forge.config import Settings

        # Test that settings can be created
        test_settings = Settings()
        assert test_settings.max_concurrent_agents >= 1

    @patch('banana_forge.config.Path.exists')
    def test_settings_file_loading(self, mock_exists):
        """Test settings file loading."""
        mock_exists.return_value = False

        # Should work even if config file doesn't exist
        from banana_forge.config import Settings
        test_settings = Settings()
        assert test_settings is not None

    def test_settings_attributes(self):
        """Test that settings has expected attributes."""
        assert hasattr(settings, 'max_concurrent_agents')
        assert hasattr(settings, 'web_search_api_key')
        assert hasattr(settings, 'context7_api_key')

        # Test default values
        assert isinstance(settings.max_concurrent_agents, int)
        assert settings.max_concurrent_agents >= 1
