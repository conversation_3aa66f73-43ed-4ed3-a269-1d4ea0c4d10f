"""
Context7 documentation tool for Banana Forge agents.

This tool provides access to up-to-date official documentation for libraries
and frameworks via the Context7 MCP server. Since Context7 API is in private
preview, we use the MCP server approach instead.
"""

import json
import logging
import os
import subprocess
import tempfile
from typing import Any

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class Context7Input(BaseModel):
    """Input for Context7 documentation tool."""

    library_name: str = Field(
        description="Name of the library or framework to get documentation for "
        "(e.g., 'fastapi', 'react', 'django')"
    )
    topic: str = Field(
        default="",
        description="Specific topic to focus on (e.g., 'authentication', "
        "'routing', 'hooks')",
    )
    tokens: int = Field(
        default=10000,
        description="Maximum number of tokens to return",
        ge=1000,
        le=20000,
    )


class Context7Tool(BaseTool):
    """
    Tool for fetching official documentation via Context7 MCP server.

    This tool uses the Context7 MCP server to get up-to-date, accurate
    documentation for libraries and frameworks. It runs the MCP server
    locally and communicates with it via stdio.
    """

    name: str = "context7_docs"
    description: str = (
        "        "Fetch official documentation for libraries and frameworks using "
        "Context7 MCP server. " "
        "Use this to get accurate, up-to-date information about APIs, "
        "usage patterns, and best practices for specific libraries."
    )
    args_schema: type[BaseModel] = Context7Input

    def _run(
        self,
        library_name: str,
        topic: str = "",
        tokens: int = 10000,
    ) -> str:
        """
        Execute the Context7 documentation search using MCP server.

        Args:
            library_name: Name of the library to search
            topic: Specific topic to focus on
            tokens: Maximum number of tokens to return

        Returns:
            Formatted string with documentation results
        """
        try:
            # Check if Context7 MCP is available
            if not self._is_context7_available():
                return (
                    f"Context7 MCP server is not available. To use Context7 "
                    f"documentation:\n"
                    f"1. Install Node.js (https://nodejs.org/)\n"
                    f"2. The Context7 MCP server will be automatically installed "
                    f"when needed\n"
                    f"3. Alternatively, install manually: npm install -g "
                    f"@upstash/context7-mcp\n\n"
                    f"For now, try searching for '{library_name}' documentation "
                    f"manually or use web search."
                )

            # First, resolve the library name to a Context7 ID
            library_id = self._resolve_library_id(library_name)
            if not library_id:
                return f"Could not find library '{library_name}' in Context7. Try a more specific name or check if the library is supported."

            # Then get the documentation
            docs = self._get_library_docs(library_id, topic, tokens)
            if not docs:
                return f"No documentation found for '{library_name}' (ID: {library_id})"

            # Format results for agent consumption
            formatted_results = []
            formatted_results.append(
                f"Documentation for '{library_name}' (Context7 ID: {library_id}):"
            )
            if topic:
                formatted_results.append(f"Topic: {topic}")
            formatted_results.append("")

            # Add the documentation content
            formatted_results.append("## Documentation Content")
            formatted_results.append(docs)

            return "\n".join(formatted_results)

        except Exception as e:
            logger.error(f"Context7 documentation search failed: {e}")
            return f"Error fetching documentation for '{library_name}': {e}"

    def _is_context7_available(self) -> bool:
        """
        Check if Context7 MCP server is available.

        Returns:
            True if Context7 MCP can be run, False otherwise
        """
        try:
            # Try to run npx --version to check if it's available
            result = subprocess.run(
                ["npx", "--version"], capture_output=True, text=True, timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False

    def _resolve_library_id(self, library_name: str) -> str | None:
        """
        Resolve a library name to a Context7-compatible library ID using MCP.

        Args:
            library_name: Name of the library to resolve

        Returns:
            Context7-compatible library ID or None if not found
        """
        try:
            # Create MCP request for resolve-library-id
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": "resolve-library-id",
                    "arguments": {"libraryName": library_name},
                },
            }

            result = self._call_mcp_server(request)
            if result and "content" in result:
                # Extract library ID from the response
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    text_content = content[0].get("text", "")
                    # Look for library ID in the response
                    if "/" in text_content:
                        # Extract the first library ID found
                        lines = text_content.split("\n")
                        for line in lines:
                            if "/" in line and not line.startswith("http"):
                                # This looks like a library ID
                                return line.strip()
                    return text_content.strip()

            return None

        except Exception as e:
            logger.error(f"Failed to resolve library ID for '{library_name}': {e}")
            return None

    def _get_library_docs(
        self, library_id: str, topic: str = "", tokens: int = 10000
    ) -> str | None:
        """
        Get documentation for a library using its Context7 ID.

        Args:
            library_id: Context7-compatible library ID
            topic: Optional topic to focus on
            tokens: Maximum tokens to return

        Returns:
            Documentation content or None if not found
        """
        try:
            # Create MCP request for get-library-docs
            arguments = {"context7CompatibleLibraryID": library_id, "tokens": tokens}

            if topic:
                arguments["topic"] = topic

            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {"name": "get-library-docs", "arguments": arguments},
            }

            result = self._call_mcp_server(request)
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    return content[0].get("text", "")
                return str(content)

            return None

        except Exception as e:
            logger.error(
                f"Failed to get documentation for library ID '{library_id}': {e}"
            )
            return None

    def _call_mcp_server(self, request: dict[str, Any]) -> dict[str, Any] | None:
        """
        Call the Context7 MCP server with a JSON-RPC request.

        Args:
            request: JSON-RPC request dictionary

        Returns:
            Response from MCP server or None if failed
        """
        try:
            # Create a temporary file for the request
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".json", delete=False
            ) as f:
                json.dump(request, f)
                request_file = f.name

            try:
                # Run the Context7 MCP server
                cmd = ["npx", "-y", "@upstash/context7-mcp@latest"]

                # Set up environment
                env = os.environ.copy()
                env["NODE_NO_WARNINGS"] = "1"  # Suppress Node.js warnings

                # Run the command with the request as stdin
                with open(request_file) as f:
                    request_json = f.read()

                process = subprocess.run(
                    cmd,
                    input=request_json,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    env=env,
                )

                if process.returncode != 0:
                    logger.error(f"Context7 MCP server failed: {process.stderr}")
                    return None

                # Parse the response
                response_lines = process.stdout.strip().split("\n")
                for line in response_lines:
                    if line.strip():
                        try:
                            response = json.loads(line)
                            if "result" in response:
                                return response["result"]
                            elif "error" in response:
                                logger.error(f"Context7 MCP error: {response['error']}")
                                return None
                        except json.JSONDecodeError:
                            continue

                return None

            finally:
                # Clean up temporary file
                try:
                    os.unlink(request_file)
                except OSError:
                    pass

        except subprocess.TimeoutExpired:
            logger.error("Context7 MCP server timed out")
            return None
        except Exception as e:
            logger.error(f"Failed to call Context7 MCP server: {e}")
            return None

    async def _arun(
        self,
        library_name: str,
        topic: str = "",
        tokens: int = 10000,
    ) -> str:
        """Async version of the tool (delegates to sync version)."""
        return self._run(library_name, topic, tokens)


def create_context7_tool() -> Context7Tool:
    """Create and return a Context7 documentation tool instance."""
    return Context7Tool()
