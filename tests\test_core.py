"""
Tests for the core functionality of Banana Forge.
"""

from unittest.mock import MagicMock, patch

import pytest

from banana_forge.core import (
    _generate_dry_run_response,
    _post_process_response,
    run_generation,
)


class TestRunGeneration:
    """Test cases for the run_generation function."""

    def test_dry_run_mode(self):
        """Test that dry run mode returns a mock response."""
        feature = "Test Feature"
        result = run_generation(feature, dry_run=True)

        assert isinstance(result, str)
        assert feature in result
        assert "[DRY RUN]" in result
        assert "Implementation Plan" in result

    def test_dry_run_with_additional_context(self):
        """Test dry run mode with additional context."""
        feature = "Test Feature"
        context = "This is additional context for testing."
        result = run_generation(feature, additional_context=context, dry_run=True)

        assert isinstance(result, str)
        assert feature in result
        assert context in result
        assert "[DRY RUN]" in result

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    def test_generation_with_mock_llm(self, mock_settings, mock_llm_class):
        """Test generation with a mocked LLM client."""
        # Set up mock
        mock_llm = MagicMock()
        mock_llm.generate_completion.return_value = (
            "# Test Feature Implementation Plan\n\nThis is a test plan."
        )
        mock_llm_class.return_value = mock_llm

        # Mock settings
        mock_settings.validate_required_settings.return_value = None
        mock_settings.verbose = False
        mock_settings.primary_model = "test-model"
        mock_settings.max_concurrent_agents = 1  # Force single-agent mode

        result = run_generation("Test Feature")

        assert isinstance(result, str)
        assert "Test Feature Implementation Plan" in result
        assert "Generated by Banana Forge" in result
        mock_llm.generate_completion.assert_called_once()

    def test_empty_feature_description(self):
        """Test that empty feature description is handled."""
        result = run_generation("", dry_run=True)
        assert isinstance(result, str)
        assert "Implementation Plan" in result


class TestDryRunResponse:
    """Test cases for the dry run response generation."""

    def test_generate_dry_run_response(self):
        """Test the dry run response generation."""
        feature = "OAuth Integration"
        context = "Need to integrate with Google OAuth"

        result = _generate_dry_run_response(feature, context)

        assert isinstance(result, str)
        assert feature in result
        assert context in result
        assert "[DRY RUN]" in result
        assert "Implementation Plan" in result

    def test_generate_dry_run_response_no_context(self):
        """Test dry run response without additional context."""
        feature = "Payment System"

        result = _generate_dry_run_response(feature, "")

        assert isinstance(result, str)
        assert feature in result
        assert "No additional context provided" in result
        assert "[DRY RUN]" in result


class TestConfigurationValidation:
    """Test configuration validation in core functions."""

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    def test_missing_api_key_raises_error(self, mock_settings, mock_llm_class):
        """Test that missing API key raises appropriate error."""
        # Mock settings to raise validation error
        mock_settings.validate_required_settings.side_effect = ValueError(
            "API key missing"
        )

        with pytest.raises(ValueError, match="API key missing"):
            run_generation("Test Feature")

    @patch("banana_forge.core.settings")
    def test_dry_run_skips_validation(self, mock_settings):
        """Test that dry run mode skips configuration validation."""
        # This should not raise an error even if validation would fail
        mock_settings.validate_required_settings.side_effect = ValueError(
            "API key missing"
        )
        mock_settings.verbose = False

        result = run_generation("Test Feature", dry_run=True)
        assert isinstance(result, str)
        assert "[DRY RUN]" in result


class TestVerboseLogging:
    """Test verbose logging functionality."""

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    @patch("banana_forge.core.logger")
    def test_verbose_logging_success(self, mock_logger, mock_settings, mock_llm_class):
        """Test verbose logging during successful generation."""
        # Set up mock
        mock_llm = MagicMock()
        mock_llm.generate_completion.return_value = (
            "# Test Feature Implementation Plan\n\nThis is a test plan."
        )
        mock_llm_class.return_value = mock_llm

        # Mock settings with verbose enabled
        mock_settings.validate_required_settings.return_value = None
        mock_settings.verbose = True
        mock_settings.primary_model = "test-model"
        mock_settings.max_concurrent_agents = 1  # Force single-agent mode

        run_generation("Test Feature")

        # Verify verbose logging was called
        mock_logger.info.assert_any_call("Generation completed successfully")

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    @patch("banana_forge.core.logger")
    def test_verbose_logging_in_simple_plan(
        self, mock_logger, mock_settings, mock_llm_class
    ):
        """Test verbose logging in _generate_simple_plan."""
        # Set up mock
        mock_llm = MagicMock()
        mock_llm.generate_completion.return_value = (
            "# Test Feature Implementation Plan\n\nThis is a test plan."
        )
        mock_llm_class.return_value = mock_llm

        # Mock settings with verbose enabled
        mock_settings.validate_required_settings.return_value = None
        mock_settings.verbose = True
        mock_settings.primary_model = "test-model"
        mock_settings.max_concurrent_agents = 1  # Force single-agent mode

        run_generation("Test Feature")

        # Verify verbose logging was called
        mock_logger.info.assert_any_call(
            "Using simple single-model generation (Phase 1 MVP)"
        )
        mock_logger.info.assert_any_call("Calling primary model: test-model")

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    @patch("banana_forge.core.logger")
    def test_exception_handling_with_verbose(
        self, mock_logger, mock_settings, mock_llm_class
    ):
        """Test exception handling with verbose mode enabled."""
        # Set up mock to raise exception
        mock_llm = MagicMock()
        mock_llm.generate_completion.side_effect = Exception("Test error")
        mock_llm_class.return_value = mock_llm

        # Mock settings with verbose enabled
        mock_settings.validate_required_settings.return_value = None
        mock_settings.verbose = True
        mock_settings.primary_model = "test-model"
        mock_settings.max_concurrent_agents = 1  # Force single-agent mode

        with pytest.raises(Exception, match="Test error"):
            run_generation("Test Feature")

        # Verify error logging was called
        mock_logger.error.assert_called_with("Generation failed: Test error")
        mock_logger.debug.assert_called_once()  # Should log traceback

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    @patch("banana_forge.core.logger")
    def test_exception_handling_without_verbose(
        self, mock_logger, mock_settings, mock_llm_class
    ):
        """Test exception handling with verbose mode disabled."""
        # Set up mock to raise exception
        mock_llm = MagicMock()
        mock_llm.generate_completion.side_effect = Exception("Test error")
        mock_llm_class.return_value = mock_llm

        # Mock settings with verbose disabled
        mock_settings.validate_required_settings.return_value = None
        mock_settings.verbose = False
        mock_settings.primary_model = "test-model"
        mock_settings.max_concurrent_agents = 1  # Force single-agent mode

        with pytest.raises(Exception, match="Test error"):
            run_generation("Test Feature")

        # Verify error logging was called but not debug
        mock_logger.error.assert_called_with("Generation failed: Test error")
        mock_logger.debug.assert_not_called()  # Should not log traceback


class TestPostProcessResponse:
    """Test post-processing functionality."""

    def test_post_process_response_normal(self):
        """Test normal post-processing."""
        response = "# Test Feature\n\nThis is a test response."
        result = _post_process_response(response, "Test Feature")

        assert "# Test Feature" in result
        assert "Generated by Banana Forge" in result
        assert result.endswith("\n")

    def test_post_process_response_empty(self):
        """Test post-processing with empty response."""
        with pytest.raises(ValueError, match="Empty response from LLM"):
            _post_process_response("", "Test Feature")

    def test_post_process_response_whitespace_only(self):
        """Test post-processing with whitespace-only response."""
        with pytest.raises(ValueError, match="Empty response from LLM"):
            _post_process_response("   \n\t  ", "Test Feature")

    def test_post_process_response_no_heading(self):
        """Test post-processing when response doesn't start with heading."""
        response = "This is a response without a heading."
        result = _post_process_response(response, "Test Feature")

        assert result.startswith("# Test Feature Implementation Plan\n\n")
        assert "This is a response without a heading." in result
        assert "Generated by Banana Forge" in result


# Phase 2 placeholder functions have been replaced with real multi-agent implementation
# Tests for the multi-agent system are in test_multi_agent.py


if __name__ == "__main__":
    pytest.main([__file__])
