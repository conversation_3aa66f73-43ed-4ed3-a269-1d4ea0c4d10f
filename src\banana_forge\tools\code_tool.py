"""
Code search tool for Banana Forge agents.

This tool provides semantic search capabilities over the indexed codebase
using ChromaDB vector store.
"""

import logging

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..vector_store import get_vector_store

logger = logging.getLogger(__name__)


class CodeSearchInput(BaseModel):
    """Input for code search tool."""

    query: str = Field(
        description="Search query describing what code to look for (e.g., 'authentication functions', 'database models', 'error handling patterns')"
    )
    max_results: int = Field(
        default=5,
        description="Maximum number of code snippets to return",
        ge=1,
        le=10,
    )


class CodeSearchTool(BaseTool):
    """
    Tool for searching the codebase using semantic similarity.

    This tool allows agents to find relevant code snippets, functions,
    classes, and patterns in the indexed codebase to inform their
    feature implementation recommendations.
    """

    name: str = "code_search"
    description: str = (
        "Search the codebase for relevant code snippets, functions, classes, "
        "or patterns. Use this to understand existing code structure, find "
        "similar implementations, or identify where new code should be added."
    )
    args_schema: type[BaseModel] = CodeSearchInput

    def _run(self, query: str, max_results: int = 5) -> str:
        """
        Execute the code search.

        Args:
            query: Search query describing what to look for
            max_results: Maximum number of results to return

        Returns:
            Formatted string with search results
        """
        try:
            vector_store = get_vector_store()
            results = vector_store.search(query, n_results=max_results)

            if not results:
                return f"No code snippets found for query: '{query}'"

            # Format results for agent consumption
            formatted_results = []
            formatted_results.append(
                f"Found {len(results)} relevant code snippets for '{query}':\n"
            )

            for i, result in enumerate(results, 1):
                metadata = result.get("metadata", {})
                file_path = metadata.get("file_path", "unknown")
                file_type = metadata.get("file_type", "")
                chunk_index = metadata.get("chunk_index", 0)
                distance = result.get("distance", 0.0)

                formatted_results.append(f"## Result {i}: {file_path}")
                if file_type:
                    formatted_results.append(f"**File Type:** {file_type}")
                formatted_results.append(f"**Relevance Score:** {1 - distance:.3f}")
                if chunk_index > 0:
                    formatted_results.append(f"**Chunk:** {chunk_index + 1}")

                # Add the code content
                content = result.get("content", "").strip()
                if content:
                    formatted_results.append("**Code:**")
                    formatted_results.append("```" + file_type.lstrip("."))
                    formatted_results.append(content)
                    formatted_results.append("```")
                else:
                    formatted_results.append("*No content available*")

                formatted_results.append("")  # Empty line between results

            return "\n".join(formatted_results)

        except Exception as e:
            logger.error(f"Code search failed: {e}")
            return f"Error searching code: {e}"

    async def _arun(self, query: str, max_results: int = 5) -> str:
        """Async version of the tool (delegates to sync version)."""
        return self._run(query, max_results)


def create_code_search_tool() -> CodeSearchTool:
    """Create and return a code search tool instance."""
    return CodeSearchTool()
