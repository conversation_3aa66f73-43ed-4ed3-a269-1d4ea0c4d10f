"""
Web search tool for Banana Forge agents.

This tool provides web search capabilities for finding external information,
best practices, and examples related to feature implementation.
"""

import logging

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..config import settings

logger = logging.getLogger(__name__)


class WebSearchInput(BaseModel):
    """Input for web search tool."""

    query: str = Field(
        description="Search query for finding relevant information on the web (e.g., 'OAuth best practices', 'React authentication patterns')"
    )
    max_results: int = Field(
        default=3,
        description="Maximum number of search results to return",
        ge=1,
        le=5,
    )


class WebSearchTool(BaseTool):
    """
    Tool for searching the web for relevant information.

    This tool allows agents to find external information, best practices,
    examples, and documentation that can inform feature implementation
    recommendations.
    """

    name: str = "web_search"
    description: str = (
        "Search the web for relevant information, best practices, examples, "
        "and documentation. Use this to find external resources that can "
        "inform feature implementation decisions."
    )
    args_schema: type[BaseModel] = WebSearchInput

    def _run(self, query: str, max_results: int = 3) -> str:
        """
        Execute the web search.

        Args:
            query: Search query
            max_results: Maximum number of results to return

        Returns:
            Formatted string with search results
        """
        try:
            # For now, return a placeholder since we don't have a web search API configured
            # In a real implementation, this would use SerpAPI, Bing Search API, or similar

            if not settings.web_search_api_key:
                return (
                    f"Web search for '{query}' would be performed here, but no search API is configured. "
                    "Consider setting up SerpAPI or similar service for web search capabilities. "
                    "For now, agents should rely on code search and documentation tools."
                )

            # Placeholder implementation - in real usage, integrate with search API
            return self._mock_search_results(query, max_results)

        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return f"Error performing web search: {e}"

    def _mock_search_results(self, query: str, max_results: int) -> str:
        """
        Generate mock search results for development/testing.

        In production, this would be replaced with actual search API calls.
        """
        mock_results = [
            {
                "title": f"Best Practices for {query}",
                "url": "https://example.com/best-practices",
                "snippet": f"Comprehensive guide covering {query} implementation patterns, security considerations, and performance optimization techniques.",
            },
            {
                "title": f"{query} - Official Documentation",
                "url": "https://docs.example.com/guide",
                "snippet": f"Official documentation and examples for implementing {query} in modern applications.",
            },
            {
                "title": f"Stack Overflow: {query} Implementation",
                "url": "https://stackoverflow.com/questions/example",
                "snippet": f"Community discussion and solutions for common {query} implementation challenges and patterns.",
            },
        ]

        # Limit to requested number of results
        results = mock_results[:max_results]

        # Format results for agent consumption
        formatted_results = []
        formatted_results.append(
            f"Found {len(results)} web search results for '{query}':\n"
        )

        for i, result in enumerate(results, 1):
            title = result["title"]
            url = result["url"]
            snippet = result["snippet"]

            formatted_results.append(f"## Result {i}: {title}")
            formatted_results.append(f"**URL:** {url}")
            formatted_results.append(f"**Summary:** {snippet}")
            formatted_results.append("")  # Empty line between results

        formatted_results.append(
            "\n*Note: These are mock results for development. "
            "Configure a web search API for real search capabilities.*"
        )

        return "\n".join(formatted_results)

    async def _arun(self, query: str, max_results: int = 3) -> str:
        """Async version of the tool (delegates to sync version)."""
        return self._run(query, max_results)


def create_web_search_tool() -> WebSearchTool:
    """Create and return a web search tool instance."""
    return WebSearchTool()
