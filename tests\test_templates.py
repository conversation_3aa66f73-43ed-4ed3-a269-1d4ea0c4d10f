"""Tests for template functionality."""

from banana_forge.templates import get_feature_template, get_simple_template


class TestTemplates:
    """Test cases for templates."""

    def test_get_feature_template_function_exists(self):
        """Test that get_feature_template function exists and is callable."""
        assert callable(get_feature_template)

    def test_get_feature_template_basic_call(self):
        """Test basic template retrieval."""
        result = get_feature_template()
        # Should return a string template
        assert isinstance(result, str)
        assert len(result) > 0

    def test_get_feature_template_content(self):
        """Test template content structure."""
        result = get_feature_template()

        # Should contain expected template sections
        assert "Implementation Plan" in result
        assert "{feature_name}" in result
        assert "{timestamp}" in result
        assert "## Overview" in result

    def test_get_feature_template_formatting(self):
        """Test template formatting."""
        result = get_feature_template()

        # Should be valid markdown format
        assert result.startswith("#")
        assert "##" in result  # Should have subsections

    def test_get_simple_template_function_exists(self):
        """Test that get_simple_template function exists and is callable."""
        assert callable(get_simple_template)

    def test_get_simple_template_basic_call(self):
        """Test simple template retrieval."""
        result = get_simple_template()
        # Should return a string template
        assert isinstance(result, str)
        assert len(result) > 0

    def test_get_simple_template_content(self):
        """Test simple template content structure."""
        result = get_simple_template()

        # Should contain expected template sections
        assert "Implementation Plan" in result
        assert "{feature_name}" in result
        assert "## Overview" in result
        assert "Generated by Banana Forge" in result
